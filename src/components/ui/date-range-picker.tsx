"use client";

import { X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface DateRangePickerProps {
  startDate?: string;
  endDate?: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  placeholder?: string;
  className?: string;
}

export function DateRangePicker({
  startDate = "",
  endDate = "",
  onStartDateChange,
  onEndDateChange,
  placeholder: _placeholder = "Select date range",
  className = ""
}: DateRangePickerProps) {

  // Clear individual date field
  const clearStartDate = () => {
    onStartDateChange("");
  };

  const clearEndDate = () => {
    onEndDateChange("");
  };

  return (
    <div className={`flex flex-col sm:flex-row gap-2 ${className}`}>
      {/* Start Date Field */}
      <div className="flex-1 min-w-0">
        <label className="block text-xs font-medium text-gray-600 mb-1">
          Start Date
        </label>
        <div className="relative">
          <Input
            type="date"
            value={startDate}
            onChange={(e) => onStartDateChange(e.target.value)}
            className="w-full text-xs h-8 border-gray-300 focus:border-blue-500 pr-12"
            placeholder="YYYY-MM-DD"
            showClearButton={false}
          />
          {startDate && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearStartDate}
              className="absolute right-6 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100"
            >
              <X className="h-3 w-3 text-gray-400" />
            </Button>
          )}
        </div>
      </div>

      {/* End Date Field */}
      <div className="flex-1 min-w-0">
        <label className="block text-xs font-medium text-gray-600 mb-1">
          End Date
        </label>
        <div className="relative">
          <Input
            type="date"
            value={endDate}
            onChange={(e) => onEndDateChange(e.target.value)}
            className="w-full text-xs h-8 border-gray-300 focus:border-blue-500 pr-12"
            placeholder="YYYY-MM-DD"
            showClearButton={false}
          />
          {endDate && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearEndDate}
              className="absolute right-6 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100"
            >
              <X className="h-3 w-3 text-gray-400" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
