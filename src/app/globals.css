@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif;
  }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--card));
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
  border-radius: 10px;
  border: 3px solid hsl(var(--card));
}

/* 列宽调整相关样式 */
.resizable-table-header {
  position: relative;
}

.column-resizer {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  z-index: 10;
  background: transparent;
  transition: background-color 0.15s ease;
}

.column-resizer:hover {
  background-color: #3b82f6;
}

.column-resizer.active {
  background-color: #2563eb;
}

/* 防止在拖拽时选择文本 */
.resizing * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 拖拽时的全局光标 */
.resizing {
  cursor: col-resize !important;
}

.resizing * {
  cursor: col-resize !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* 日期输入框样式优化 - 最大化紧凑布局 */
input[type="date"] {
  position: relative;
  padding-right: 20px !important; /* 进一步减少右边距 */
}

/* 消除所有内部间距和边距 */
input[type="date"]::-webkit-datetime-edit {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

input[type="date"]::-webkit-datetime-edit-fields-wrapper {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

/* 消除所有日期字段的右边距 */
input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field,
input[type="date"]::-webkit-datetime-edit-year-field {
  margin-right: 0 !important;
  padding-right: 0 !important;
}

/* 特别针对年份字段后的间距 - 最大化压缩 */
input[type="date"]::-webkit-datetime-edit-year-field {
  margin-right: -4px !important; /* 更大的负边距，最大化压缩 */
  padding-right: 0 !important;
}

/* 压缩日期字段之间的分隔符间距 */
input[type="date"]::-webkit-datetime-edit-text {
  padding: 0 1px !important; /* 最小化分隔符间距 */
}

/* 确保整个日期编辑区域紧凑 */
input[type="date"]::-webkit-datetime-edit {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0 !important;
}

/* 隐藏日期输入框的默认占位符文本，只在空值时隐藏 */
input[type="date"]:not(:focus):invalid::-webkit-datetime-edit-text,
input[type="date"]:not(:focus):invalid::-webkit-datetime-edit-month-field,
input[type="date"]:not(:focus):invalid::-webkit-datetime-edit-day-field,
input[type="date"]:not(:focus):invalid::-webkit-datetime-edit-year-field {
  color: transparent;
}

/* 最大化紧凑的日历图标定位 */
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 1 !important;
  cursor: pointer;
  margin-left: -4px !important; /* 更大的负边距，让图标紧贴文本 */
  margin-right: 2px !important;
  padding: 0 !important;
  width: 16px;
  height: 16px;
  background-size: 16px;
  position: relative;
  /* 确保图标不会被裁剪 */
  z-index: 1;
}

/* 为空的日期输入框添加自定义占位符样式 */
.date-input-wrapper {
  position: relative;
}

.date-input-wrapper input[type="date"]:invalid + .date-placeholder {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  font-size: 0.75rem;
}

.date-input-wrapper input[type="date"]:focus + .date-placeholder,
.date-input-wrapper input[type="date"]:valid + .date-placeholder {
  display: none;
}

/* 多行文本截断样式 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  max-height: calc(1.4em * 3);
}
